; Configuration file for RS485 application
; Generated at Jun 12 2025 09:35:09

[ProtocolList]
list=RS485

[RS485]
Port = COM3
BaudRate = 9600
StopBits = 2
Parity = N
DataBits = 8
Timeout = 1.0

;Boiler name list
[BoilerList]
list=分解炉1号

; ==================== 烟气分析仪配置 ====================
; 分解炉1号烟气分析仪的详细配置参数
[分解炉1号]
Desc = 水泥厂分解炉1号                    ; 设备描述信息
Protocol = RS485                        ; 通讯协议类型，使用RS485串口通讯
CollectionInterval = 3                 ; 数据采集间隔（秒），每15秒采集一次数据
; ==================== Modbus设备地址配置 ====================
; 以下数值为各传感器在RS485总线上的Modbus设备地址（十进制）
; 每个传感器都是独立的Modbus设备，通过设备地址进行区分和访问
; 读取时使用：read_holding_registers(fd, 设备地址, 0x0000, 1, registers, false)

Current = 60                           ; 抽气泵电流传感器设备地址，读取值×0.001，单位：A
Voltage = 50                           ; 压力表传感器设备地址，读取值×0.001，单位：kPa
O2 = 4                                 ; O2（氧气）传感器设备地址，读取值×0.01，单位：%
Co = 1                                 ; CO（一氧化碳）传感器设备地址，直接使用原始值，单位：ppm
Tempature = 40                         ; 冷凝器温度传感器设备地址，读取值×0.1，单位：℃
Switch1 = 80                           ; 反吹反馈开关量信号设备地址，从4096(0x1000)开始读取第一个DI状态，返回0（停止）或1（运行），默认值为0（停止状态）

; ==================== 反吹反馈控制配置 ====================
; 反吹反馈开关量信号控制逻辑：
; 1. 当系统检测到反吹反馈【开关量信号】运行时（Switch1=1），MonitoringSystem.qml和DataScreenView.qml页面上氧气、一氧化碳数值停止更新
; 2. 当系统检测到反馈【开关量信号】停止时（Switch1=0），延迟指定时间后，恢复氧气、一氧化碳数值的实时更新
; 3. 延迟时间可通过以下参数配置，单位：秒
BackflowDelayTime = 60                 ; 反吹反馈停止后的延迟恢复时间，单位：秒，默认60秒

;DCS设备列表
[DCSList]
list=DCS1

[DCS1]
Desc = 分布式控制系统1
Protocol = DCS_RS485
CollectionInterval = 20 ; 统一采集间隔（秒）- 32位程序稳定性优化：单线程模式，同时采集所有参数

AssociatedBoiler = 分解炉1号

; ==================== DCS通信方式配置 ====================
; 使用 OPC DA（OLE通信）协议与DCS系统通信
; 注意：这里使用的是OPC DA协议，不是OPC UA
CommunicationType = OPC ; 通信方式：使用OPC DA协议

; ==================== OPC DA通信配置 ====================
; 当 CommunicationType = OPC 时使用以下配置
; 注意：这是OPC DA (Data Access) 协议，基于Windows COM技术

; OPC服务器连接配置 - 支持自动发现和手动指定两种方式
; 方式1：自动发现模式 - 输入IP地址，程序自动遍历发现OPC服务器
OPCServerHost = *********** ; OPC服务器IP地址，程序将自动发现该IP上的OPC服务器
OPCAutoDiscovery = true ; 启用自动发现模式，程序会遍历指定IP上的所有OPC服务器

; 方式2：手动指定模式 - 直接指定服务器标识符（当自动发现失败时使用）
; 根据你的demo发现的服务器信息进行配置：
; 服务器名称：Freelance2000OPCServer.129.1
; 服务器ClassID：{F822DE8F-207C-11D1-BAD4-006097385129}
OPCServerProgID = Freelance2000OPCServer.129.1 ; 从demo发现的服务器ProgID
OPCServerClassID = {F822DE8F-207C-11D1-BAD4-006097385129} ; 从demo发现的服务器ClassID

; 备用服务器列表（程序会依次尝试连接）
OPCServerProgIDList = Freelance2000OPCServer.129.1,Matrikon.OPC.Simulation.1,Kepware.KEPServerEX.V6,RSLinx.OPC.1

OPCUpdateRate = 2000 ; OPC组数据更新频率（毫秒）- 32位程序稳定性优化：从1000ms增加到2000ms
                     ; 建议值：500-2000ms，根据系统性能和实时性要求调整

OPCGroupName = DCS_Group ; OPC组名称，可自定义，建议使用有意义的名称

; OPC读取标签配置 - 数据采集标签
; 以下标签名称需要根据实际OPC服务器的标签结构进行修改
; 标签命名规则通常为：[设备名].[参数组].[参数名]
OPCFurnaceSetTempTag =  ; 炉膛设定温度读取标签（DCS待新建）(℃)
OPCFurnaceActualTempTag = AI5101T01_R ; 炉膛实际温度读取标签 (℃)
OPCActualFurnacePressureTag = AI5101P01_R ; 实际炉压读取标签 (kPa)
OPCCoalFeedRateTag = 761RS01_SF_M ; 给煤量读取标签 (t/h)
OPCActualRawMaterial1Tag = AI5202F01_R ; 实际生料量1读取标签 (t/h)
OPCActualRawMaterial2Tag = XSST_SF_R ; 实际生料量2读取标签 (t/h)
OPCPlannedRawMaterialTag =  ; 计划生料量读取标签（DCS待新建）(t/h)
OPCInducedDraftFanSpeedTag = AI5401ARS1_R ; 引风机转速读取标签 (rpm)
; 新增四个设定值标签 - 用于读取DCS系统中的设定值
OPCCoalFeedSetTag = 761RS01_SG_M ; 给煤量设定读取标签 (t/h)
OPCRawMaterial1SetTag = AO5202F01_R ; 生料量1设定读取标签 (t/h)
OPCRawMaterial2SetTag = XSST_SG_R ; 生料量2设定读取标签 (t/h)
OPCInducedDraftFanSetTag = AO5401S2_R1 ; 引风机转速设定读取标签 (rpm)

; ==================== 临时参数配置 ====================
; 由于现在没有建立炉膛设定温度读取标签和计划生料量读取标签，创建两个字段，临时炉膛设定温度和临时计划生料量
TempFurnaceSetTemp = 1000.0 ; 临时炉膛设定温度（℃），范围800-1200℃，默认1000℃
TempPlannedRawMaterial = 100.0 ; 临时计划生料量（t/h），默认5.0t/h


; OPC写入标签配置 - 参数调整控制标签
; 用于自动调整功能向DCS发送控制指令
; 设定值写入标签配置（用于参数自动调整功能）
; 写入标签通常与读取标签不同，可能有专门的写入点或控制点
OPCCoalFeedSetWriteTag = DCS.Coal.FeedSetWrite ; 给煤量设定写入标签
OPCRawMaterial1SetWriteTag = DCS.RawMaterial1.SetWrite ; 生料量1设定写入标签
OPCRawMaterial2SetWriteTag = DCS.RawMaterial2.SetWrite ; 生料量2设定写入标签
OPCInducedDraftFanSetWriteTag = DCS.Fan.InducedDraftSetWrite ; 引风机转速设定写入标签

; 参数调整配置
[ParameterAdjustment]
; ==================== 调整模式配置 ====================
AutoAdjustmentEnabled = false            ; 自动调整模式开关（true=自动调整，false=手动调整）

; 新增一个字段，输出的是生料量1设定还是生料量2设定
RawMaterialSetpointTarget = 2 ; 生料量设定调整目标：1=调整生料量1设定，2=调整生料量2设定，默认为2

; ==================== 调整间隔配置 ====================
AdjustmentCheckInterval = 3    ; 参数调整检查间隔（每N次采集检查一次，即60秒调整一次）

; ==================== 前置条件：生料量调节配置 ====================
; 生料量必须先达标，其他参数才能进行调节
; 注意：DCS数据为t/h单位，但内部计算转换为kg/h进行精确计算
RawMaterialDiffThreshold = 10.0      ; 生料量差值阈值（kg/h，|实际-计划| ≤ 10kg/h，内部计算单位）
RawMaterialAdjustmentStep = 5.0      ; 生料量调节步长（kg/h，实际>计划时减5kg/h，实际<计划时加5kg/h，内部计算单位）

; ==================== 步骤1：炉膛温度判断与给煤量调节 ====================
; 判断炉膛温度差值 → 超出±10度时调节给煤量 → 回到步骤1
FurnaceTempDiffThreshold = 10.0      ; 炉膛温度差值阈值（℃，|实际-设定| ≤ 10℃）
FurnaceTempMaxValue = 1200.0         ; 炉膛设定温度最大值（℃）
FurnaceTempMinValue = 800.0          ; 炉膛设定温度最小值（℃）

; 给煤量调节参数（步骤1执行）
; 注意：DCS数据为t/h单位，但内部计算转换为kg/h进行精确计算
CoalFeedAdjustmentStep = 10.0        ; 给煤量调节步长（kg/h，实际>设定时减10kg/h，实际<设定时加10kg/h，内部计算单位）
CoalFeedMaxValue = 50.0              ; 给煤量调节最大值（t/h，DCS显示单位）
CoalFeedMinValue = 0.0               ; 给煤量调节最小值（t/h，DCS显示单位）

; ==================== 步骤2：氧气浓度判断与生料量/煤量调节 ====================
; 炉膛温度在±10℃内 → 判断氧气浓度差值 → 超出范围时调节生料量或煤量 → 回到步骤1
OxygenConcentrationDiffThreshold = 0.1  ; 氧气浓度差值阈值（%，|实际-设定| ≤ 0.1%）
OxygenConcentrationSetpoint = 3.5       ; 设定氧气浓度（%）

; 氧气浓度调节参数（步骤2执行）
; 注意：DCS数据为t/h单位，但内部计算转换为kg/h进行精确计算
OxygenRawMaterialAdjustmentStep = 3.0   ; 氧气浓度调节生料量步长（kg/h，浓度>设定时增加生料量，内部计算单位）
OxygenCoalFeedAdjustmentStep = 5.0      ; 氧气浓度调节煤量步长（kg/h，浓度<设定时减少煤量，内部计算单位）

; ==================== 步骤3：炉压判断与引风机调节 ====================
; 氧气浓度在范围内 → 判断炉压差值 → 超出范围时调节引风机频率 → 回到步骤1
FurnacePressureDiffThreshold = 1.0   ; 炉压差值阈值（Pa，|实际-给定| ≤ 1Pa）
FurnacePressureSetpoint = -75.0      ; 给定炉压（Pa，默认值-75Pa，范围-50至-100）
FurnacePressureSetpointMin = -100.0  ; 给定炉压调节最小值（Pa）
FurnacePressureSetpointMax = -50.0   ; 给定炉压调节最大值（Pa）

; 引风机调节参数（步骤3执行）
InducedDraftFanAdjustmentStep = 0.1  ; 引风机转速调节步长（rpm，实际>给定时减0.1rpm，实际<给定时加0.1rpm）
InducedDraftFanMaxSpeed = 50.0   ; 引风机调节最大转速（rpm）
InducedDraftFanMinSpeed = 10.0   ; 引风机调节最小转速（rpm）

; ==================== 控制流程说明 ====================
; 每次流程都从第0步开始，每个流程循环调节只调节一次，判断使用实际的，调整使用设定的。
;0. 初始判断实际总生料量（实际生料量1+实际生料量2）是否达标计划生料量 →（必须达标才能进行后续判断）,如果没有达标调整生料量1设定、或生料量2设定
;1. 判断炉膛温度差值 →
;- 超出温度范围：判断炉膛实际温度和设定温度，如果实际温度大于设定温度则减少给煤量设定 → 本次循环调节结束，如果实际温度小于设定温度则增加给煤量设定 → 本次循环调节结束
;- 在范围内：进入步骤2
;
;2. 判断氧气浓度差值 →
;- 超出范围：判断氧气浓度和设定氧气浓度，如果大于，增加生料量1设定或生料量2设定 → 本次循环调节结束，如果小于，减少给煤量设定 → 本次循环调节结束
;- 在范围内：进入步骤3
;
;3. 判断炉压差值 →
;- 超出范围：判断实际炉压和给定炉压，如果大于，减少引风机转速设定 → 本次循环调节结束，如果小于，增加引风机转速设定 → 本次循环调节结束，
;- 在范围内：系统达产

; 如果是手动调整，则只需要把给出的调整建议值发送到ParameterAdjustmentView.qml页面上去，如果是自动调整，不仅要把调整的建议值发送到ParameterAdjustmentView.qml页面上，还需要发送到DCS
; ParameterAdjustmentView.qml也是一样一次只显示一个调整值。因为一个控制流程只会调整一次
; 由于现在OPCFurnaceSetTempTag炉膛设定温度读取标签和OPCPlannedRawMaterialTag计划生料量读取标签还没有，所以创建两个字段，临时炉膛设定温度和临时计划生料量
; 后续的流程判断，这两个字段为空，直接使用这两个临时变量的值就好了
