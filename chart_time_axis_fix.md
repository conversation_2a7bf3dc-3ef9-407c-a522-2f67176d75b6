# 图表时间轴问题修复

## 🔍 问题描述
- **现象**: 运行20多分钟后，8小时、12小时、24小时视图的曲线图已经填满，但1小时视图正常
- **根本原因**: 相对时间计算错误和抽样逻辑问题

## 🐛 原始问题分析

### 1. 相对时间计算错误
```cpp
// 原来的错误计算方式
double relativeTimeHours = (m_dataCount * collectionIntervalSeconds) / (60.0 * 60.0);
```
- 使用 `m_dataCount` 累计计数，导致时间轴一直增长
- 不是基于实际的数据开始时间计算

### 2. 抽样逻辑问题
- 抽样从数据开始位置开始，不是从显示窗口开始
- 可能跳过应该显示的最新数据
- 没有考虑时间偏移对抽样的影响

### 3. 时间偏移计算基于错误的相对时间
- 导致显示窗口位置不正确

## ✅ 修复方案

### 1. 修复相对时间计算
```cpp
// 新的正确计算方式
QDateTime currentTime = QDateTime::currentDateTime();
if (!m_dataStartTimeSet) {
    m_dataStartTime = currentTime;
    m_dataStartTimeSet = true;
}

qint64 elapsedSeconds = m_dataStartTime.secsTo(currentTime);
double relativeTimeHours = elapsedSeconds / 3600.0;
double relativeTimeMinutes = elapsedSeconds / 60.0;
```

### 2. 优化抽样逻辑
```cpp
// 计算有效的数据范围，只处理显示窗口内的数据
int startIndex = 0;
if (timeOffset > 0 && m_smokeO2Data.size() > 100) {
    // 找到合适的起始索引以提高性能
    for (int i = 0; i < m_smokeO2Data.size(); i++) {
        QVariantMap point = m_smokeO2Data[i].toMap();
        qreal displayTime = point["x"].toReal() - timeOffset;
        if (displayTime >= -1.0) {
            startIndex = qMax(0, i - samplingInterval);
            break;
        }
    }
}
```

### 3. 增强调试信息
- 添加时间偏移计算的调试输出
- 显示当前时间、显示范围、偏移量等关键信息

## 🎯 预期效果

### 修复后的行为
1. **时间轴正确性**: 所有视图的时间轴都从0开始，按实际时间流逝计算
2. **显示范围准确**: 
   - 1小时视图: 显示最近60分钟
   - 8小时视图: 显示最近8小时
   - 12小时视图: 显示最近12小时  
   - 24小时视图: 显示最近24小时
3. **性能优化**: 只处理显示窗口内的数据，提高渲染性能

### 测试验证点
1. 启动程序后，各个时间视图应该从0开始显示
2. 运行20分钟后：
   - 1小时视图: 显示0-20分钟的数据
   - 8小时视图: 显示0-20分钟的数据（占用很小一部分）
   - 12小时视图: 显示0-20分钟的数据（占用很小一部分）
   - 24小时视图: 显示0-20分钟的数据（占用很小一部分）
3. 运行超过各视图时间范围后，应该看到滑动窗口效果

## 🔧 关键修改文件
- `monitoring_datasource.cpp`: 修复相对时间计算和抽样逻辑
- 主要修改的方法:
  - `updateSmokeData()`: 相对时间计算
  - `updateSmokeChartSeries()`: 抽样和时间偏移逻辑
  - `updateSmokeChartSeriesWithMinutes()`: 分钟视图时间偏移

## 📊 调试日志关键信息
- `数据开始时间已设置: YYYY-MM-DD HH:mm:ss`
- `时间偏移计算: 当前时间=X.XXX小时, 显示范围=X.X小时, 偏移量=X.XXX小时`
- `图表更新完成: zoomIndex=X, 显示范围=X.X小时, O2系列点数=X, CO系列点数=X, 时间偏移=X.XXX小时`
